/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 */
//@cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default"
//@v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)"
// @cliArgs --board /ti/boards/LP_MSPM0G3507 --rtos nortos

/**
 * Import the modules used in this configuration.
 */
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");

/**
 * Write custom configuration values to the imported modules.
 */

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

SYSCTL.forceDefaultClkConfig = true;
